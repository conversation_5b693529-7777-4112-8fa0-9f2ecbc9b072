<?php

namespace Xmetr\RealEstate\Http\Controllers\Fronts;

use Xmetr\Base\Http\Controllers\BaseController;
use Xmetr\Base\Http\Responses\BaseHttpResponse;
use Xmetr\RealEstate\Repositories\Interfaces\PropertyInterface;
use Xmetr\RealEstate\Supports\RealEstateHelper;
use Illuminate\Http\Request;

class PropertyFilterController extends BaseController
{
    public function __construct(protected PropertyInterface $propertyRepository)
    {
    }

    public function getPropertiesCount(Request $request): BaseHttpResponse
    {
        // Validate the request parameters
        $request->validate([
            'keyword' => 'nullable|string|max:255',
            'type' => 'nullable|string',
            'bedroom' => 'nullable|integer|min:0',
            'bathroom' => 'nullable|integer|min:0',
            'floor' => 'nullable|integer|min:0',
            'min_square' => 'nullable|numeric|min:0',
            'max_square' => 'nullable|numeric|min:0',
            'min_price' => 'nullable|numeric|min:0',
            'max_price' => 'nullable|numeric|min:0',
            'project' => 'nullable|string',
            'project_id' => 'nullable|integer',
            'category' => 'nullable|string',
            'category_id' => 'nullable|integer',
            'author_id' => 'nullable|integer',
            'account_type' => 'nullable|string',
            'bills_included' => 'nullable|string',
            'furnished' => 'nullable|string',
            'pets_allowed' => 'nullable|string',
            'smoking_allowed' => 'nullable|string',
            'online_view_tour' => 'nullable|string',
            'country_id' => 'nullable|integer',
            'city_id' => 'nullable|integer',
            'country' => 'nullable|string',
            'city' => 'nullable|string',
            'state' => 'nullable|string',
            'state_id' => 'nullable|integer',
            'location' => 'nullable|string',
            'features' => 'nullable|array',
            'features.*' => 'integer',
            'suitable' => 'nullable|array',
            'suitable.*' => 'string',
            'spoken_languages' => 'nullable|array',
            'spoken_languages.*' => 'integer',
            'required_documents' => 'nullable|array',
            'required_documents.*' => 'string',
        ]);

        // Prepare filters array similar to RealEstateHelper::getPropertiesFilter
        $filters = array_merge([
            'keyword' => null,
            'type' => null,
            'bedroom' => null,
            'bathroom' => null,
            'floor' => null,
            'min_square' => null,
            'max_square' => null,
            'min_price' => null,
            'max_price' => null,
            'project' => null,
            'project_id' => null,
            'category' => null,
            'category_id' => null,
            'author_id' => null,
            'account_type' => null,
            'bills_included' => null,
            'furnished' => null,
            'pets_allowed' => null,
            'smoking_allowed' => null,
            'online_view_tour' => null,
            'country_id' => null,
            'city_id' => null,
            'country' => null,
            'city' => null,
            'state' => null,
            'state_id' => null,
            'location' => null,
            'features' => null,
            'suitable' => null,
            'spoken_languages' => null,
            'required_documents' => null,
        ], $request->only([
            'type', 'bedroom', 'bathroom', 'floor', 'min_square', 'max_square',
            'min_price', 'max_price', 'project', 'project_id', 'category', 'category_id',
            'author_id', 'account_type', 'bills_included', 'furnished', 'pets_allowed',
            'smoking_allowed', 'online_view_tour', 'country_id', 'city_id', 'country',
            'city', 'state', 'state_id', 'location', 'features', 'suitable',
            'spoken_languages', 'required_documents'
        ]));

        // Handle keyword parameter (mapped from 'k' to 'keyword')
        $filters['keyword'] = $request->input('k');
        $filters['category'] = $request->input('category');

        // Get count using the same filtering logic as the main properties query
        // but without pagination and with count() instead of get()
        $count = $this->propertyRepository->getPropertiesCount($filters);

        return $this
            ->httpResponse()
            ->setData(['count' => $count]);
    }
}
