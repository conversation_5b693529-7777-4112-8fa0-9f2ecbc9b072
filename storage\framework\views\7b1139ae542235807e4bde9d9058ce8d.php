<?php
    Theme::set('breadcrumbEnabled', 'no');
    Theme::set('headerClass', 'no-line');

    Theme::asset()
        ->container('footer')
        ->add('location-js', 'vendor/core/plugins/location/js/location.js?v=1.0.0', ['jquery']);

    $title = SeoHelper::getTitleOnly();

    // Get current filter parameters for JavaScript
    $currentFilters = [
        'country_id' => request()->query('country_id'),
        'city_id' => request()->query('city_id'),
        'category_id' => request()->query('category_id'),
        'bedroom' => request()->query('bedroom'),
        'bathroom' => request()->query('bathroom'),
        'min_price' => request()->query('min_price'),
        'max_price' => request()->query('max_price'),
        'type' => request()->query('type'),
    ];

    // Remove null values
    $currentFilters = array_filter($currentFilters, function($value) {
        return $value !== null && $value !== '';
    });

    // Check for excluded filters
    $hasExtraFilters = request()->hasAny([
        'suitable',
        'account_type',
        'floor',
        'min_price',
        'max_price',
        'furnished',
        'smoking_allowed',
        'features',
        'spoken_languages',
    ]);
        $countryName = null;
        $cityName = null;
    // Only apply custom SEO title if no extra filters
    if (!$hasExtraFilters) {

        $category = request()->query('category') ? get_property_category_by_slug(request()->query('category')) : null;
        $categoryName = $category ? $category->name : null;

        $bedrooms = request()->query('bedroom');



        // Start with category SEO if available
        if ($category) {
            $category_seo = $category->getMetadata('seo_meta', true);
            if ($category) {
                $categoryName = $category->name;
                $category_seo = $category->getMetadata('seo_meta', true);
                $category_seo_title = $category_seo['seo_title'] ?? $category->name;
            }
        }

        // Get country & city names
        if (request()->query('country_id')) {
            $countryName = get_country_name_by_id(request()->query('country_id'));
        }

        if (request()->query('city_id')) {
            $cityName = get_city_name_by_id(request()->query('city_id'));
        }

        // Only build custom title if there is at least country, city, category, or bedrooms
         if ($categoryName && !$countryName && !$cityName && !$bedrooms) {
            $title = $category_seo_title;
            SeoHelper::setTitle($title);

        } elseif ($countryName || $cityName || $categoryName || $bedrooms) {

            if ($categoryName && $bedrooms && $countryName && $cityName) {
                $template = 'Long-Term :bedrooms Bedrooms :category Rentals in :country, :city';
            } elseif ($categoryName && $bedrooms && $countryName) {
                $template = 'Long-Term :bedrooms Bedrooms :category Rentals in :country';
            } elseif ($bedrooms && $countryName && $cityName) {
                $template = 'Long-Term :bedrooms Bedrooms Rentals in :country, :city';
            } elseif ($bedrooms && $countryName) {
                $template = 'Long-Term :bedrooms Bedrooms Rentals in :country';
            } elseif ($categoryName && $countryName && $cityName) {
                $template = 'Long-Term :category Rentals in :country, :city';
            } elseif ($categoryName && $countryName) {
                $template = 'Long-Term :category Rentals in :country';
            } elseif ($countryName && $cityName) {
                $template = 'Long-Term Rentals in :country, :city';
            } elseif ($countryName) {
                $template = 'Long-Term Rentals in :country';
            } else {
                $template = 'Long-Term Rentals';
            }

            $finalTitle = __($template, [
                'category' => $categoryName,
                'bedrooms' => $bedrooms,
                'country' => $countryName,
                'city' => $cityName,
            ]);
            $title  = $finalTitle;
            SeoHelper::setTitle($finalTitle);
        }
    }
    else {
            SeoHelper::meta()->addMeta('robots', 'noindex, nofollow');
            $title = SeoHelper::getTitleOnly();
            SeoHelper::setTitle($title);
        }

    if ($properties->count() == 0) {
        $title = __('No properties found.');
        SeoHelper::meta()->addMeta('robots', 'noindex, nofollow');
    }

    if (request()->has('page')){
        SeoHelper::meta()->addMeta('robots', 'noindex, nofollow');
    }

    $filteredQuery = collect(request()->query())
        ->except('page')
        ->all();

?>

<div class="signup-modal">
    <div class="modal  x-modal-scrollOffset" id="modalFilterSettings" aria-hidden="true"
        aria-labelledby="modalFilterSettingsLabel" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered" style="box-shadow:0px 5px 15px rgba(0, 0, 0, 0.25);">


            <input type="hidden" name="page" value="<?php echo e(BaseHelper::stringify(request()->integer('page'))); ?>" />
            <input type="hidden" name="layout" value="<?php echo e(BaseHelper::stringify(request()->input('layout'))); ?>" />

            <div class="modal-content">
                <form action="<?php echo e($actionUrl); ?>" data-url="<?php echo e($ajaxUrl); ?>" data-type="properties" method="get" class="filter-form">
                    <?php echo csrf_field(); ?>
                    <div class="modal-header !px-[20px] !py-[15px]">
                        <h5 class="modal-title text-[20px] text-black font-bold" id="modalFilterSettingsLabel">
                            <?php echo e(__('Search by parameters')); ?></h5>
                            <button type="button"
                                class="w-[50px] h-[50px] rounded-[10px] bg-[#212329] flex justify-center items-center hover:bg-[#3e424d]"
                                data-bs-dismiss="modal" aria-label="Close">
                                <svg width="18" height="18" viewBox="0 0 18 18" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path d="M2 2L16.1421 16.1421M2 16.1421L16.1421 2" stroke="white" stroke-width="3"
                                        stroke-linecap="round" stroke-linejoin="round" />
                                </svg>
                            </button>
                    </div>
                    <div class="modal-body x-fixedParent_wrapper w-full rounded-[15px] bg-white p-[20px] flex flex-col gap-[32px] relative" id="x-filtersWrapper">
                        <!-- Add x-button-toggle--active in x-button-toggle to activate button -->
                        <div class="flex flex-col gap-[20px] w-full">
                            <h5 class="title !text-[15px]"><?php echo e(__('Posted by')); ?></h5>
                            <?php
                                $accountTypes = \Xmetr\RealEstate\Enums\AccountTypeEnum::labels();
                            ?>
                            <div class="grid grid-cols-3 gap-[12px] x-button-toggle--singleSelect">
                                <label class="x-radio-toggle max-[460px]:px-[4px]">
                                    <input type="radio" name="account_type" value=""
                                        <?php if(request()->query('account_type') == ''): ?> checked <?php endif; ?>>
                                    <p><?php echo e(__('All')); ?></p>
                                </label>
                                <?php $__currentLoopData = $accountTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php if($value=='developer'): ?> <?php continue; ?>; <?php endif; ?>
                                    <label class="x-radio-toggle max-[460px]:px-[4px]">
                                        <input type="radio" name="account_type" value="<?php echo e($value); ?>"
                                            <?php if(request()->query('account_type') == $value): ?> checked <?php endif; ?>>
                                        <p><?php echo e($label); ?></p>
                                    </label>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                        <span class="block w-full h-[1px] bg-[#DDDDDD]"></span>
                        <!-- Type -->
                        <div class="flex flex-col gap-[20px] w-full">
                            <h5 class="title !text-[15px]"><?php echo e(__('Object type')); ?></h5>

                            <div class="grid grid-cols-2 gap-[12px] x-button-toggle--singleSelect">

                                <?php
                                    $categories = get_property_categories([
                                        'indent' => '↳',
                                        'conditions' => ['status' => \Xmetr\Base\Enums\BaseStatusEnum::PUBLISHED],
                                    ]);
                                ?>
                                <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <label class="x-radio-toggle max-[460px]:px-[4px]">
                                        <input type="radio" name="category" value="<?php echo e($category->slugable->key); ?>"
                                            <?php if(request()->query('category') == $category->slugable->key): ?> checked <?php endif; ?>>
                                        <p><?php echo e($category->name); ?></p>
                                    </label>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                            </div>
                        </div>
                        <span class="block w-full h-[1px] bg-[#DDDDDD]"></span>


                        <!-- Rooms -->
                        <div class="flex flex-col gap-[20px] w-full x-button-toggle--singleSelect">
                            <h5 class="title !text-[15px]"><?php echo e(__('Rooms')); ?></h5>

                            <div class="grid grid-cols-4 gap-[12px]">

                                <?php $__currentLoopData = range(1, 4); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $i): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php if($i < 4): ?>
                                        <label class="x-radio-toggle max-[460px]:px-[4px]">
                                            <input type="radio" name="bedroom" value="<?php echo e($i); ?>"
                                                <?php if(request()->query('bedroom') == $i): ?> checked <?php endif; ?>>
                                            <p><?php echo e($i); ?></p>
                                        </label>
                                    <?php else: ?>
                                        <label class="x-radio-toggle max-[460px]:px-[4px]">
                                            <input type="radio" name="bedroom" value="<?php echo e($i); ?>"
                                                <?php if(request()->query('bedroom') == $i): ?> checked <?php endif; ?>>
                                            <p><?php echo e($i); ?> +</p>
                                        </label>
                                    <?php endif; ?>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>


                            </div>
                        </div>

                        <span class="block w-full h-[1px] bg-[#DDDDDD]"></span>

                        <!-- Location -->
                        <div class="flex flex-col gap-[20px] w-full">
                            <h5 class="title !text-[15px]"><?php echo e(__('Location')); ?></h5>

                            <div class="border border-[#DDDDDD] rounded-[10px] px-[8px] w-full h-[50px]">

                                <select class="x-select x-select-city w-full h-full" id="filter-district"
                                    data-filter="country" name="country_id" data-type="country" autocomplete="country">
                                    
                                    <option value="" selected><?php echo e(__('Choose Country')); ?></option>
                                    <?php $__currentLoopData = get_all_countries(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $country): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($country->id); ?>" data-slug="<?php echo e($country->slug); ?>"
                                            data-url="<?php echo e($country->url); ?>" <?php if(request()->query('country_id') == $country->id): echo 'selected'; endif; ?>>
                                            <?php echo e($country->name); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                                </select>
                            </div>

                            <div class="border border-[#DDDDDD] rounded-[10px] px-[8px] w-full h-[50px]" id="filter-city_wrapper" style="display: none;">
                                
                                <select class="x-select x-select-city w-full h-full" id="filter-district" name="city_id"
                                    data-filter="city" data-type="city"
                                    data-url="<?php echo e(route('public.ajax-active-listing-cities-by-country')); ?>">
                                    
                                    <option value="" selected><?php echo e(__('Choose City')); ?></option>


                                    <?php if(request()->query('country_id')): ?>
                                        <?php $__currentLoopData = get_all_cities(request()->query('country_id')); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $city): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($city->id); ?>" data-slug="<?php echo e($city->slug); ?>"
                                                data-url="<?php echo e($city->url); ?>" <?php if(request()->query('city_id') == $city->id): echo 'selected'; endif; ?>>
                                                <?php echo e($city->name); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    <?php else: ?>
                                        <?php if($cityName): ?>
                                            <option value="<?php echo e(request()->query('city_id')); ?>" selected>
                                                <?php echo e($cityName); ?></option>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                </select>
                            </div>

                        </div>

                        <span class="block w-full h-[1px] bg-[#DDDDDD]"></span>

                        <!-- Rent price -->
                        <div class="flex flex-col gap-[20px] w-full">
                            <h5 class="title !text-[15px]"><?php echo e(__('Price $/month')); ?>.</h5>

                            

                            <div class="grid grid-cols-2 gap-[12px]">
                                <input id="filter-rent_from" type="number" name="min_price"
                                    placeholder="<?php echo e(__('To')); ?>"
                                    value="<?php echo e(BaseHelper::stringify(request()->float('min_price'))); ?>"
                                    class="px-[20px] py-[15px] placeholder:text-black placeholder:text-[15px] text-black text-[15px] border border-[#DDDDDD] rounded-[10px] max-[460px]:px-[8px]"
                                    min="0">
                                <input id="filter-rent_to" type="number" name="max_price"
                                    placeholder="<?php echo e(__('From')); ?>"
                                    value="<?php echo e(BaseHelper::stringify(request()->float('max_price'))); ?>"
                                    class="px-[20px] py-[15px] placeholder:text-black placeholder:text-[15px] text-black text-[15px] border border-[#DDDDDD] rounded-[10px] max-[460px]:px-[8px]"
                                    min="0">
                            </div>
                        </div>

                        <div class="flex flex-col gap-[20px] w-full">
                            <label class="flex items-center gap-[10px] max-[460px]:px-[4px]">
                                <input type="checkbox" name="bills_included" value="1"
                                    <?php if(request()->query('bills_included') == '1'): echo 'checked'; endif; ?> />
                                <p><?php echo e(__('Bills Included')); ?></p>
                            </label>
                        </div>

                        <span class="block w-full h-[1px] bg-[#DDDDDD]"></span>
                         <div class="flex flex-col gap-[32px]">
                            <div class="flex flex-col gap-[20px] w-full">
                                <h5 class="title !text-[15px]"><?php echo e(__('Highlights')); ?></h5>
                                <div class="grid grid-cols-2 gap-[12px] max-[460px]:grid-cols-1">
                                    <label class="x-checkbox-toggle max-[460px]:px-[4px]">
                                        <input type="checkbox" name="furnished" <?php if(request()->query('furnished') == '1'): echo 'checked'; endif; ?> />
                                        <p><?php echo e(__('Furnished')); ?></p>
                                    </label>
                                    <label class="x-checkbox-toggle max-[460px]:px-[4px]">
                                        <input type="checkbox" name="pets_allowed" <?php if(request()->query('pets_allowed') == '1'): echo 'checked'; endif; ?> />
                                        <p><?php echo e(__('Pets Allowed')); ?></p>
                                    </label>
                                    <label class="x-checkbox-toggle max-[460px]:px-[4px]">
                                        <input type="checkbox" name="smoking_allowed" <?php if(request()->query('smoking_allowed') == '1'): echo 'checked'; endif; ?> />
                                        <p><?php echo e(__('Smoking Allowed')); ?></p>
                                    </label>
                                    <label class="x-checkbox-toggle max-[460px]:px-[4px]">
                                        <input type="checkbox" name="online_view_tour" <?php if(request()->query('online_view_tour') == '1'): echo 'checked'; endif; ?> />
                                        <p><?php echo e(__('Online View Tour')); ?></p>
                                    </label>
                                </div>
                            </div>
                        </div>
                        <span class="block w-full h-[1px] bg-[#DDDDDD]"></span>

                        <div class="flex flex-col gap-[32px]">
                            <!-- Amenities -->
                            <div class="flex flex-col gap-[20px] w-full">
                                <h5 class="title !text-[15px]"><?php echo e(__('Amenities')); ?></h5>

                                <?php
                                    $features = \Xmetr\RealEstate\Models\Feature::query()->has('properties')->wherePublished()->get();
                                ?>

                                <div id="feature-list" class="grid grid-cols-2 gap-[12px] max-[460px]:grid-cols-1 overflow-hidden transition-all duration-300">
                                    <?php $__currentLoopData = $features; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <label class="x-checkbox-toggle max-[460px]:px-[4px] feature-item <?php echo e($index >= 4 ? 'hidden' : ''); ?>">
                                            <input type="checkbox" name="features[]"
                                                value="<?php echo e($feature->getKey()); ?>" <?php if(in_array($feature->getKey(), request()->query('features', []))): echo 'checked'; endif; ?> />
                                            <p><?php echo e($feature->name); ?></p>
                                        </label>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>

                                <?php if($features->count() > 4): ?>
                                    <button type="button" id="toggle-feature-btn" class="text-black text-sm hover:underline w-fit">
                                        <?php echo e(__('Show More')); ?>

                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>

                        
                        <script>
                            document.addEventListener('DOMContentLoaded', function () {
                                const toggleBtn = document.getElementById('toggle-feature-btn');
                                const featureItems = document.querySelectorAll('.feature-item');

                                if (!toggleBtn) return;

                                let expanded = false;

                                toggleBtn.addEventListener('click', function () {
                                    expanded = !expanded;

                                    featureItems.forEach((item, index) => {
                                        if (index >= 4) {
                                            item.classList.toggle('hidden', !expanded);
                                        }
                                    });

                                    toggleBtn.textContent = expanded ? '<?php echo e(__('Show Less')); ?>' : '<?php echo e(__('Show More')); ?>';
                                });
                            });
                        </script>
 
                        
<div class="flex flex-col gap-[32px]">

    <?php
        $spokenLanguages = \Xmetr\RealEstate\Models\SpokenLanguage::query()
            ->wherePublished()
            ->orderBy('name')
            ->get();
        $asGrid ??= true;
    ?>


<div class="accordion-section flex flex-col gap-[12px] w-full border rounded-xl p-4 shadow-sm">
    <button type="button" class="accordion-toggle flex items-center justify-between w-full cursor-pointer">
        <h5 class="title !text-[15px] font-semibold"><?php echo e(__('Host Speak')); ?></h5>
        <svg class="w-4 h-4 transition-transform duration-200 accordion-arrow" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M19 9l-7 7-7-7" />
        </svg>
    </button>

    <div class="accordion-content hidden mt-2">
         <div class="grid grid-cols-2 gap-[12px] max-[460px]:grid-cols-1">
                <?php $__currentLoopData = $spokenLanguages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $language): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <fieldset class="spoken-language-filter-item flex items-center gap-[10px] max-[460px]:px-[4px]">
                        <input
                            type="checkbox"
                            name="spoken_languages[]"
                            class="tf-checkbox style-1"
                            id="spoken-language-<?php echo e($language->getKey()); ?>"
                            value="<?php echo e($language->getKey()); ?>"
                            <?php if(in_array($language->getKey(), request()->query('spoken_languages', []))): echo 'checked'; endif; ?>
                        />
                        <label for="spoken-language-<?php echo e($language->getKey()); ?>" class="text-cb-amenities d-flex align-items-center">
                            <?php if($language->flag): ?>
                                <span class="flag-container me-2">
                                    <?php echo language_flag($language->flag, $language->name, 16); ?>

                                </span>
                            <?php endif; ?>
                            <span class="language-name"><?php echo e($language->name); ?></span>
                        </label>
                    </fieldset>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
    </div>
</div>
 
</div>
 

 
                        <div class="flex flex-col gap-[32px]">

                             <?php
                                        use Xmetr\RealEstate\Enums\SuitableForEnum;

                                        $suitable = collect(SuitableForEnum::labels())->map(function ($label, $value) {
                                            return (object) [
                                                'id' => $value,
                                                'name' => $label,
                                            ];
                                        });
                                    ?>
                            <div class="accordion-section flex flex-col gap-[12px] w-full border rounded-xl p-4 shadow-sm">
                                    <button type="button" class="accordion-toggle flex items-center justify-between w-full cursor-pointer">
                                        <h5 class="title !text-[15px] font-semibold"><?php echo e(__('Suitable For')); ?></h5>
                                        <svg class="w-4 h-4 transition-transform duration-200 accordion-arrow" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M19 9l-7 7-7-7" />
                                        </svg>
                                </button>

                                    <div class="accordion-content hidden mt-2">
                                        <div class="grid grid-cols-2 gap-[12px] max-[460px]:grid-cols-1">

                        

                                            <?php $__currentLoopData = $suitable; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $suitableItem): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <label class="x-checkbox-toggle max-[460px]:px-[4px]">
                                                    <input type="checkbox" name="suitable[]"
                                                        value="<?php echo e($suitableItem->id); ?>" <?php if(in_array($suitableItem->id, request()->query('suitable', []))): echo 'checked'; endif; ?> />
                                                    <p><?php echo e($suitableItem->name); ?></p>
                                                </label>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>


                                        </div>
                                    </div>
                            </div>
                            
                        </div>



                        <div class="x-fixedParent_end invisible"></div>

                        <!-- Menu -->
                        <div class="fixed -left-[9px] bottom-0 p-[20px] flex justify-center gap-[10px] w-full"
                            id="modalFilterSettingsInner">
                            <div class="flex justify-between gap-[10px] max-w-[440px] w-full">
                                <a href="<?php echo e($actionUrl); ?>"
                                    class="x-filtersReset text-center bg-[#FBF0EE] px-[20px] py-[12px] duration-200 hover:bg-[#DC6F5A] [&>p]:hover:text-white rounded-[10px] grow z-[3]">
                                    <p class="text-[#DC6F5A] text-[15px] duration-200 font-bold"><?php echo e(__('Clear All')); ?>

                                    </p>
                                </a>

                                <div class="form-style">
                                    <div class="property-count-display mb-2" id="property-count-display" style="display: none;">
                                        <span class="text-muted">
                                            <span id="property-count-number">0</span> <?php echo e(__('properties found')); ?>

                                            <span id="property-count-loading" style="display: none;">
                                                <i class="fa fa-spinner fa-spin"></i>
                                            </span>
                                        </span>
                                    </div>
                                </div>

                                <button data-bs-toggle="modal" type="submit"
                                    class="bg-[#5E2DC2] px-[20px] py-[12px] duration-200 hover:bg-[#5026a5] rounded-[10px] flex items-center gap-[10px] relative max-[1024px]:px-[15px] shrink-0 max-[768px]:h-[50px] grow justify-center z-[3]">
                                    <p class="text-white text-[15px] font-bold text-center"><?php echo e(__('Search')); ?></p>
                                </button>
                            </div>

                            <div class="x-fixedParent_shadow absolute bottom-0 left-0 w-full h-[95px]"
                                style="background: linear-gradient(180deg, rgba(33, 35, 41, 0),rgba(33, 35, 41, .8));">
                            </div>
                        </div>
                    </div>
                </form>
            </div>

        </div>
    </div>

    <div class="hiddenbar-body-ovelay"></div>
</div>

<!-- Map add x-filter-map--active to activate -->
<div class="x-filter-map fixed top-[88px] max-[1024px]:top-[71px] left-0 w-screen h-screen bg-white z-[20]"
    id="x-filter-map">
    <div
        class="container flex flex-row justify-end pt-[20px] max-[1024px]:!px-[20px] max-[1024px]:m-0 max-[1024px]:max-w-none">
        <button type="button"
            class="x-filter-map_trigger w-[50px] h-[50px] rounded-[10px] bg-[#212329] flex justify-center items-center hover:bg-[#3e424d] z-[3]">
            <svg width="18" height="18" viewBox="0 0 18 18" fill="none"
                xmlns="http://www.w3.org/2000/svg">
                <path d="M2 2L16.1421 16.1421M2 16.1421L16.1421 2" stroke="white" stroke-width="3"
                    stroke-linecap="round" stroke-linejoin="round" />
            </svg>
        </button>
    </div>

    <div class="h-full">
        <div class="absolute bottom-[88px] max-[1024px]:bottom-[71px] left-0 w-full p-[20px] flex justify-center">
            <a href="#modalFilterSettings" role="button" data-bs-toggle="modal"
                class="x-fixedParent_trigger bg-[#5E2DC2] px-[20px] py-[12px] duration-200 hover:bg-[#5026a5] rounded-[10px] flex items-center gap-[10px] relative max-[1024px]:px-[15px] shrink-0 max-[768px]:h-[50px] w-fit z-[3]">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none"
                    xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M16.48 16.5368H19M2.68 10L1 10.044M2.68 10C2.68 11.3255 3.75451 12.4 5.08 12.4C6.40548 12.4 7.48 11.3255 7.48 10C7.48 8.6745 6.40548 7.6 5.08 7.6C3.75451 7.6 2.68 8.6745 2.68 10ZM8.169 10.0441H19M10.801 3.55124H1M19 3.55124H16.48M1 16.5368H10.801M15.88 16.6C15.88 17.9255 14.8055 19 13.48 19C12.1545 19 11.08 17.9255 11.08 16.6C11.08 15.2745 12.1545 14.2 13.48 14.2C14.8055 14.2 15.88 15.2745 15.88 16.6ZM15.88 3.4C15.88 4.72548 14.8055 5.8 13.48 5.8C12.1545 5.8 11.08 4.72548 11.08 3.4C11.08 2.07452 12.1545 1 13.48 1C14.8055 1 15.88 2.07452 15.88 3.4Z"
                        stroke="white" stroke-width="2" stroke-linecap="round" />
                </svg>

                <p class="text-white text-[15px] font-bold"><?php echo e(__('Filters')); ?></p>


                <p
                    class="x-filtersToggled w-[20px] h-[20px] text-center text-white text-[11px] font-bold bg-[#DC6F5A] rounded-[32px] hidden">
                </p>
            </a>

            <div class="absolute bottom-0 left-0 w-full h-[95px]"
                style="background: linear-gradient(180deg, rgba(33, 35, 41, 0),rgba(33, 35, 41, .8));"></div>
        </div>

        <div id="x-filters-map" class="w-full h-full absolute top-0 left-0">
            <?php echo $__env->make(Theme::getThemeNamespace('views.real-estate.partials.map'), ['mapUrl' => $mapUrl ?? route('public.ajax.properties.map')], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        </div>
    </div>
</div>

<div class="wrapper ovh py-[24px] pb-[40px]">
    <div class="container flex flex-col gap-[30px]">
        <div class="flex flex-col gap-[15px]">
            <div class="flex flex-col gap-[10px]">
                <h1 class="title" id="filter-page-title">
                    <?php echo e($title); ?>

                </h1>

                <p class="text-[15px] text-[#717171]"><?php echo e(__('Found')); ?> – <span
                        id="total-record-found"><?php echo e($properties instanceof \Illuminate\Pagination\LengthAwarePaginator ? $properties->total() : $properties->count()); ?></span>
                    <?php echo e(__('listings')); ?></p>
            </div>

            <div class="flex justify-between items-stretch gap-[16px]">
                <div class="flex items-stretch gap-[10px] w-full">

                    

                    <a href="#modalFilterSettings" role="button" data-bs-toggle="modal"
                        class="x-fixedParent_trigger bg-[#5E2DC2] px-[20px] py-[12px] duration-200 hover:bg-[#5026a5] rounded-[10px] flex items-center gap-[10px] relative max-[1024px]:px-[15px] shrink-0 max-[768px]:h-[50px]">
                        <svg width="20" height="20" viewBox="0 0 20 20" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M16.48 16.5368H19M2.68 10L1 10.044M2.68 10C2.68 11.3255 3.75451 12.4 5.08 12.4C6.40548 12.4 7.48 11.3255 7.48 10C7.48 8.6745 6.40548 7.6 5.08 7.6C3.75451 7.6 2.68 8.6745 2.68 10ZM8.169 10.0441H19M10.801 3.55124H1M19 3.55124H16.48M1 16.5368H10.801M15.88 16.6C15.88 17.9255 14.8055 19 13.48 19C12.1545 19 11.08 17.9255 11.08 16.6C11.08 15.2745 12.1545 14.2 13.48 14.2C14.8055 14.2 15.88 15.2745 15.88 16.6ZM15.88 3.4C15.88 4.72548 14.8055 5.8 13.48 5.8C12.1545 5.8 11.08 4.72548 11.08 3.4C11.08 2.07452 12.1545 1 13.48 1C14.8055 1 15.88 2.07452 15.88 3.4Z"
                                stroke="white" stroke-width="2" stroke-linecap="round" />
                        </svg>

                        <p class="text-white text-[15px] font-bold "><?php echo e(__('Filters')); ?></p>

                        <p
                            class="x-filtersToggled hidden w-[20px] h-[20px] text-center text-white text-[11px] font-bold bg-[#DC6F5A] rounded-[32px] max-[1024px]:absolute max-[1024px]:-top-[10px] max-[1024px]:-right-[10px] max-[1024px]:border-[3px] max-[1024px]:border-white max-[1024px]:w-[26px] max-[1024px]:h-[26px]">
                        </p>
                    </a>

                    <a href="<?php echo e($actionUrl); ?>"
                        class="x-filtersReset bg-[#FBF0EE] px-[20px] py-[12px] duration-200 hover:bg-[#DC6F5A] [&>p]:hover:text-white rounded-[10px] reset-filter-btn"
                        style="<?php echo \Illuminate\Support\Arr::toCssStyles(['display: none' => empty($filteredQuery)]) ?>">
                        <p class="text-[#DC6F5A] text-[15px] duration-200 font-bold"><?php echo e(__('Reset')); ?></p>
                    </a>

                    
                </div>

                <button
                    class="x-filter-map_trigger shrink-0 rounded-[10px] bg-[#212329] hover:bg-[#3e424d] flex justify-center items-center gap-[10px] px-[20px] h-auto max-[768px]:hidden">
                    <svg width="20" height="18" viewBox="0 0 20 18" fill="none"
                        xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M6.72604 1.60466V13.4768M13.274 3.99746V16.2166M1 5.05669V13.9425C1 15.6777 2.23288 16.39 3.73059 15.5316L5.87671 14.3078C6.34246 14.043 7.11872 14.0156 7.60273 14.2622L12.3973 16.664C12.8813 16.9014 13.6575 16.8832 14.1233 16.6183L18.0776 14.3535C18.5799 14.0613 19 13.3489 19 12.7645V3.87861C19 2.14346 17.7671 1.43113 16.2694 2.28957L14.1233 3.51332C13.6575 3.77816 12.8813 3.80555 12.3973 3.55898L7.60273 1.16629C7.11872 0.928845 6.34246 0.94711 5.87671 1.21195L1.92237 3.47679C1.41096 3.76902 1 4.48135 1 5.05669Z"
                            stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    </svg>

                    <p class="text-white text-[15px]"><?php echo e(__('Map')); ?></p>
                </button>
            </div>
        </div>

        <div class="position-relative" data-bb-toggle="data-listing">
            <?php echo $__env->make($itemsViewPath, compact('itemLayout'), \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        </div>

    </div>


    <div
        class="fixed left-0 bottom-0 w-full hidden max-[768px]:flex gap-[10px] items-stretch py-[20px] px-[30px] z-[3]">
        <button
            class="x-filter-map_trigger shrink-0 rounded-[10px] bg-[#212329] hover:bg-[#3e424d] flex justify-center items-center gap-[10px] px-[20px] h-auto z-[4] grow flex-1">
            <svg width="20" height="18" viewBox="0 0 20 18" fill="none"
                xmlns="http://www.w3.org/2000/svg">
                <path
                    d="M6.72604 1.60466V13.4768M13.274 3.99746V16.2166M1 5.05669V13.9425C1 15.6777 2.23288 16.39 3.73059 15.5316L5.87671 14.3078C6.34246 14.043 7.11872 14.0156 7.60273 14.2622L12.3973 16.664C12.8813 16.9014 13.6575 16.8832 14.1233 16.6183L18.0776 14.3535C18.5799 14.0613 19 13.3489 19 12.7645V3.87861C19 2.14346 17.7671 1.43113 16.2694 2.28957L14.1233 3.51332C13.6575 3.77816 12.8813 3.80555 12.3973 3.55898L7.60273 1.16629C7.11872 0.928845 6.34246 0.94711 5.87671 1.21195L1.92237 3.47679C1.41096 3.76902 1 4.48135 1 5.05669Z"
                    stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
            </svg>

            <p class="text-white text-[15px]"><?php echo e(__('Map')); ?></p>
        </button>

        <a href="#modalFilterSettings" role="button" data-bs-toggle="modal"
            class="x-fixedParent_trigger bg-[#5E2DC2] px-[20px] py-[12px] duration-200 hover:bg-[#5026a5] rounded-[10px] flex items-center justify-center gap-[10px] relative shrink-0 z-[4] grow flex-1">
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none"
                xmlns="http://www.w3.org/2000/svg">
                <path
                    d="M16.48 16.5368H19M2.68 10L1 10.044M2.68 10C2.68 11.3255 3.75451 12.4 5.08 12.4C6.40548 12.4 7.48 11.3255 7.48 10C7.48 8.6745 6.40548 7.6 5.08 7.6C3.75451 7.6 2.68 8.6745 2.68 10ZM8.169 10.0441H19M10.801 3.55124H1M19 3.55124H16.48M1 16.5368H10.801M15.88 16.6C15.88 17.9255 14.8055 19 13.48 19C12.1545 19 11.08 17.9255 11.08 16.6C11.08 15.2745 12.1545 14.2 13.48 14.2C14.8055 14.2 15.88 15.2745 15.88 16.6ZM15.88 3.4C15.88 4.72548 14.8055 5.8 13.48 5.8C12.1545 5.8 11.08 4.72548 11.08 3.4C11.08 2.07452 12.1545 1 13.48 1C14.8055 1 15.88 2.07452 15.88 3.4Z"
                    stroke="white" stroke-width="2" stroke-linecap="round" />
            </svg>

            <p class="text-white text-[15px] font-bold"><?php echo e(__('Filters')); ?></p>

            <p
                class="x-filtersToggled w-[20px] h-[20px] text-center text-white text-[11px] font-bold bg-[#DC6F5A] rounded-[32px] hidden">
            </p>
        </a>

        <div class="absolute bottom-0 left-0 w-full h-[95px]"
            style="background: linear-gradient(180deg, rgba(33, 35, 41, 0),rgba(33, 35, 41, .8));"></div>
    </div>

</div>

<script>
// Make current filters available to JavaScript
window.currentPageFilters = <?php echo json_encode($currentFilters, 15, 512) ?>;
console.log('Current page filters:', window.currentPageFilters);
</script>
<?php /**PATH D:\laragon\www\xmetr\platform\themes/xmetr/views/real-estate/listing-layouts/xmetr.blade.php ENDPATH**/ ?>