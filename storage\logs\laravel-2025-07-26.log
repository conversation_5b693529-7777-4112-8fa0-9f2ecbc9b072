[2025-07-26 00:05:20] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-26 00:05:29] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-26 00:05:29] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-26 00:05:29] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-26 00:05:29] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-26 00:05:29] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-26 00:05:29] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-26 00:05:29] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-26 00:30:00] local.ERROR: syntax error, unexpected token "=", expecting "]" {"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \"=\", expecting \"]\" at D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\functions\\functions.php:725)
[stacktrace]
#0 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(149): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#1 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Filesystem\\Filesystem->requireOnce('D:\\\\laragon\\\\www\\\\...')
#2 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Supports\\Helper.php(27): Illuminate\\Support\\Facades\\Facade::__callStatic('requireOnce', Array)
#3 D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Providers\\ThemeManagementServiceProvider.php(72): Xmetr\\Base\\Supports\\Helper::autoload('D:\\\\laragon\\\\www\\\\...')
#4 D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Providers\\ThemeManagementServiceProvider.php(39): Xmetr\\Theme\\Providers\\ThemeManagementServiceProvider->registerAutoloadPathFromTheme('xmetr')
#5 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Xmetr\\Theme\\Providers\\ThemeManagementServiceProvider->boot()
#6 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#7 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#8 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#9 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#10 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#11 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Xmetr\\Theme\\Providers\\ThemeManagementServiceProvider))
#12 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Xmetr\\Theme\\Providers\\ThemeManagementServiceProvider), 113)
#13 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1039): array_walk(Array, Object(Closure))
#14 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#15 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#16 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#17 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#18 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 {main}
"} 
[2025-07-26 00:30:00] local.ERROR: syntax error, unexpected token "=", expecting "]" {"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \"=\", expecting \"]\" at D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\functions\\functions.php:725)
[stacktrace]
#0 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(149): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#1 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Filesystem\\Filesystem->requireOnce('D:\\\\laragon\\\\www\\\\...')
#2 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Supports\\Helper.php(27): Illuminate\\Support\\Facades\\Facade::__callStatic('requireOnce', Array)
#3 D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Providers\\ThemeManagementServiceProvider.php(72): Xmetr\\Base\\Supports\\Helper::autoload('D:\\\\laragon\\\\www\\\\...')
#4 D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Providers\\ThemeManagementServiceProvider.php(39): Xmetr\\Theme\\Providers\\ThemeManagementServiceProvider->registerAutoloadPathFromTheme('xmetr')
#5 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Xmetr\\Theme\\Providers\\ThemeManagementServiceProvider->boot()
#6 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#7 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#8 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#9 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#10 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#11 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Xmetr\\Theme\\Providers\\ThemeManagementServiceProvider))
#12 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Xmetr\\Theme\\Providers\\ThemeManagementServiceProvider), 113)
#13 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1039): array_walk(Array, Object(Closure))
#14 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#15 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#16 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#17 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#18 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 {main}
"} 
[2025-07-26 00:53:42] local.ERROR: SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (Connection: mysql, SQL: select `name`, `id` from `pages` where `status` = published) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (Connection: mysql, SQL: select `name`, `id` from `pages` where `status` = published) at D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(983): Illuminate\\Database\\Connection->runQueryCallback('select `name`, ...', Array, Object(Closure))
#1 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(962): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select `name`, ...', Array, Object(Closure))
#2 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(785): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select `name`, ...', Array, Object(Closure))
#3 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select `name`, ...', Array, Object(Closure))
#4 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2913): Illuminate\\Database\\Connection->select('select `name`, ...', Array, true)
#5 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3142): Illuminate\\Database\\Query\\Builder->runSelect()
#6 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3138): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(889): Illuminate\\Database\\Query\\Builder->pluck('name', 'id')
#9 D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Providers\\HookServiceProvider.php(562): Illuminate\\Database\\Eloquent\\Builder->pluck('name', 'id')
#10 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1102): Xmetr\\RealEstate\\Providers\\HookServiceProvider->Xmetr\\RealEstate\\Providers\\{closure}(Object(Illuminate\\Foundation\\Application))
#11 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1045): Illuminate\\Foundation\\Application->fireAppCallbacks(Array)
#12 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#13 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#14 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#15 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#16 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it at D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:65)
[stacktrace]
#0 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(65): PDO->__construct('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#1 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(83): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'root', '', Array)
#2 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(48): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection(Object(PDOException), 'mysql:host=127....', 'root', '', Array)
#3 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#4 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#6 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1339): call_user_func(Object(Closure))
#7 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1375): Illuminate\\Database\\Connection->getPdo()
#8 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(528): Illuminate\\Database\\Connection->getReadPdo()
#9 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect(true)
#10 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select `name`, ...', Array)
#11 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(983): Illuminate\\Database\\Connection->runQueryCallback('select `name`, ...', Array, Object(Closure))
#12 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(962): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select `name`, ...', Array, Object(Closure))
#13 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(785): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select `name`, ...', Array, Object(Closure))
#14 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select `name`, ...', Array, Object(Closure))
#15 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2913): Illuminate\\Database\\Connection->select('select `name`, ...', Array, true)
#16 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3142): Illuminate\\Database\\Query\\Builder->runSelect()
#17 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#18 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3138): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#19 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(889): Illuminate\\Database\\Query\\Builder->pluck('name', 'id')
#20 D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Providers\\HookServiceProvider.php(562): Illuminate\\Database\\Eloquent\\Builder->pluck('name', 'id')
#21 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1102): Xmetr\\RealEstate\\Providers\\HookServiceProvider->Xmetr\\RealEstate\\Providers\\{closure}(Object(Illuminate\\Foundation\\Application))
#22 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1045): Illuminate\\Foundation\\Application->fireAppCallbacks(Array)
#23 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#24 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#25 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#26 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#27 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 {main}
"} 
[2025-07-26 16:01:08] local.WARNING: DB query exceeded 559.85 ms. SQL: select * from `menus` where `status` = ?  
[2025-07-26 16:26:08] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\PropertyStatusEnum  
[2025-07-26 16:26:26] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\RentalPeriodEnum  
[2025-07-26 16:26:43] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\RentalPeriodEnum  
[2025-07-26 16:26:45] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\RentalPeriodEnum  
[2025-07-26 16:27:40] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\RentalPeriodEnum  
[2025-07-26 16:33:02] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\RentalPeriodEnum  
[2025-07-26 16:54:39] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\RentalPeriodEnum  
[2025-07-26 16:55:38] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\RentalPeriodEnum  
[2025-07-26 18:27:08] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-26 18:27:14] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-26 18:27:14] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-26 18:27:14] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-26 18:27:14] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-26 18:27:14] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-26 18:27:14] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-26 18:27:14] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
