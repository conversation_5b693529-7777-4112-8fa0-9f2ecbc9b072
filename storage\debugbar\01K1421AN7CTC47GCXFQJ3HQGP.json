{"__meta": {"id": "01K1421AN7CTC47GCXFQJ3HQGP", "datetime": "2025-07-26 19:05:57", "utime": **********.16081, "method": "GET", "uri": "/en/ajax/properties/map?page=53", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753556755.838965, "end": **********.160853, "duration": 1.3218879699707031, "duration_str": "1.32s", "measures": [{"label": "Booting", "start": 1753556755.838965, "relative_start": 0, "end": **********.5802, "relative_end": **********.5802, "duration": 0.****************, "duration_str": "741ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.580212, "relative_start": 0.****************, "end": **********.160858, "relative_end": 5.0067901611328125e-06, "duration": 0.****************, "duration_str": "581ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.597578, "relative_start": 0.***************, "end": **********.616325, "relative_end": **********.616325, "duration": 0.*****************, "duration_str": "18.75ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.69155, "relative_start": 0.****************, "end": **********.157866, "relative_end": **********.157866, "duration": 0.*****************, "duration_str": "466ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "61MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.3.17", "Environment": "local", "Debug Mode": "Enabled", "URL": "xmetr.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 10, "nb_statements": 10, "nb_visible_statements": 10, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.008820000000000001, "accumulated_duration_str": "8.82ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `slugs_translations` where `lang_code` = 'en' and `key` = 'properties' limit 1", "type": "query", "params": [], "bindings": ["en", "properties"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Middleware/RedirectIncorrectLanguagePrefix.php", "file": "D:\\laragon\\www\\xmetr\\app\\Http\\Middleware\\RedirectIncorrectLanguagePrefix.php", "line": 98}, {"index": 15, "namespace": null, "name": "app/Http/Middleware/RedirectIncorrectLanguagePrefix.php", "file": "D:\\laragon\\www\\xmetr\\app\\Http\\Middleware\\RedirectIncorrectLanguagePrefix.php", "line": 46}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.627593, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "RedirectIncorrectLanguagePrefix.php:98", "source": {"index": 14, "namespace": null, "name": "app/Http/Middleware/RedirectIncorrectLanguagePrefix.php", "file": "D:\\laragon\\www\\xmetr\\app\\Http\\Middleware\\RedirectIncorrectLanguagePrefix.php", "line": 98}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fapp%2FHttp%2FMiddleware%2FRedirectIncorrectLanguagePrefix.php:98", "ajax": false, "filename": "RedirectIncorrectLanguagePrefix.php", "line": "98"}, "connection": "xmetr", "explain": null, "start_percent": 0, "width_percent": 8.277}, {"sql": "select * from `slugs` where `key` = 'properties' limit 1", "type": "query", "params": [], "bindings": ["properties"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Middleware/RedirectIncorrectLanguagePrefix.php", "file": "D:\\laragon\\www\\xmetr\\app\\Http\\Middleware\\RedirectIncorrectLanguagePrefix.php", "line": 107}, {"index": 15, "namespace": null, "name": "app/Http/Middleware/RedirectIncorrectLanguagePrefix.php", "file": "D:\\laragon\\www\\xmetr\\app\\Http\\Middleware\\RedirectIncorrectLanguagePrefix.php", "line": 46}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.629846, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "RedirectIncorrectLanguagePrefix.php:107", "source": {"index": 14, "namespace": null, "name": "app/Http/Middleware/RedirectIncorrectLanguagePrefix.php", "file": "D:\\laragon\\www\\xmetr\\app\\Http\\Middleware\\RedirectIncorrectLanguagePrefix.php", "line": 107}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fapp%2FHttp%2FMiddleware%2FRedirectIncorrectLanguagePrefix.php:107", "ajax": false, "filename": "RedirectIncorrectLanguagePrefix.php", "line": "107"}, "connection": "xmetr", "explain": null, "start_percent": 8.277, "width_percent": 3.855}, {"sql": "select count(*) as aggregate from `re_properties` where (`re_properties`.`moderation_status` = 'approved' and `re_properties`.`status` != 'not_available') and (`expire_date` >= '2025-07-26 19:05:56' or `never_expired` = 1) and `status` != 'rented'", "type": "query", "params": [], "bindings": ["approved", "not_available", "2025-07-26 19:05:56", 1, "rented"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 366}, {"index": 17, "namespace": null, "name": "platform/plugins/real-estate/src/Repositories/Eloquent/PropertyRepository.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Repositories\\Eloquent\\PropertyRepository.php", "line": 473}, {"index": 18, "namespace": null, "name": "platform/themes/xmetr/src/Http/Controllers/XmetrController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\src\\Http\\Controllers\\XmetrController.php", "line": 85}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.647997, "duration": 0.00157, "duration_str": "1.57ms", "memory": 0, "memory_str": null, "filename": "RepositoriesAbstract.php:366", "source": {"index": 16, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 366}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsupport%2Fsrc%2FRepositories%2FEloquent%2FRepositoriesAbstract.php:366", "ajax": false, "filename": "RepositoriesAbstract.php", "line": "366"}, "connection": "xmetr", "explain": null, "start_percent": 12.132, "width_percent": 17.8}, {"sql": "select * from `re_properties` where (`re_properties`.`moderation_status` = 'approved' and `re_properties`.`status` != 'not_available') and (`expire_date` >= '2025-07-26 19:05:56' or `never_expired` = 1) and `status` != 'rented' order by `created_at` desc limit 20 offset 1040", "type": "query", "params": [], "bindings": ["approved", "not_available", "2025-07-26 19:05:56", 1, "rented"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 366}, {"index": 18, "namespace": null, "name": "platform/plugins/real-estate/src/Repositories/Eloquent/PropertyRepository.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Repositories\\Eloquent\\PropertyRepository.php", "line": 473}, {"index": 19, "namespace": null, "name": "platform/themes/xmetr/src/Http/Controllers/XmetrController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\src\\Http\\Controllers\\XmetrController.php", "line": 85}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.650522, "duration": 0.0039, "duration_str": "3.9ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 29.932, "width_percent": 44.218}, {"sql": "select `id`, `key`, `prefix`, `reference_id` from `slugs` where `slugs`.`reference_id` in (94, 96, 97, 98, 100, 101, 102, 104, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117) and `slugs`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property'", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Property"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 366}, {"index": 24, "namespace": null, "name": "platform/plugins/real-estate/src/Repositories/Eloquent/PropertyRepository.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Repositories\\Eloquent\\PropertyRepository.php", "line": 473}, {"index": 25, "namespace": null, "name": "platform/themes/xmetr/src/Http/Controllers/XmetrController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\src\\Http\\Controllers\\XmetrController.php", "line": 85}], "start": **********.661617, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 74.15, "width_percent": 5.215}, {"sql": "select `id`, `name` from `states` where `states`.`id` in (188, 192)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 366}, {"index": 24, "namespace": null, "name": "platform/plugins/real-estate/src/Repositories/Eloquent/PropertyRepository.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Repositories\\Eloquent\\PropertyRepository.php", "line": 473}, {"index": 25, "namespace": null, "name": "platform/themes/xmetr/src/Http/Controllers/XmetrController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\src\\Http\\Controllers\\XmetrController.php", "line": 85}], "start": **********.6680691, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 79.365, "width_percent": 3.515}, {"sql": "select `id`, `name` from `cities` where `cities`.`id` in (8175, 8189, 8208)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 366}, {"index": 24, "namespace": null, "name": "platform/plugins/real-estate/src/Repositories/Eloquent/PropertyRepository.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Repositories\\Eloquent\\PropertyRepository.php", "line": 473}, {"index": 25, "namespace": null, "name": "platform/themes/xmetr/src/Http/Controllers/XmetrController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\src\\Http\\Controllers\\XmetrController.php", "line": 85}], "start": **********.671689, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 82.88, "width_percent": 3.515}, {"sql": "select `id`, `is_default`, `exchange_rate`, `symbol`, `title`, `is_prefix_symbol` from `re_currencies` where `re_currencies`.`id` in (1, 4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 366}, {"index": 24, "namespace": null, "name": "platform/plugins/real-estate/src/Repositories/Eloquent/PropertyRepository.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Repositories\\Eloquent\\PropertyRepository.php", "line": 473}, {"index": 25, "namespace": null, "name": "platform/themes/xmetr/src/Http/Controllers/XmetrController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\src\\Http\\Controllers\\XmetrController.php", "line": 85}], "start": **********.675045, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 86.395, "width_percent": 3.288}, {"sql": "select `re_categories`.`id`, `re_categories`.`name`, `re_property_categories`.`property_id` as `pivot_property_id`, `re_property_categories`.`category_id` as `pivot_category_id` from `re_categories` inner join `re_property_categories` on `re_categories`.`id` = `re_property_categories`.`category_id` where `re_property_categories`.`property_id` in (94, 96, 97, 98, 100, 101, 102, 104, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117) and `status` = 'published' order by `created_at` desc, `is_default` desc, `order` desc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 366}, {"index": 22, "namespace": null, "name": "platform/plugins/real-estate/src/Repositories/Eloquent/PropertyRepository.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Repositories\\Eloquent\\PropertyRepository.php", "line": 473}, {"index": 23, "namespace": null, "name": "platform/themes/xmetr/src/Http/Controllers/XmetrController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\src\\Http\\Controllers\\XmetrController.php", "line": 85}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.68303, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 19, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 89.683, "width_percent": 4.762}, {"sql": "select * from `re_currencies` order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/real-estate/src/Supports/CurrencySupport.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Supports\\CurrencySupport.php", "line": 107}, {"index": 17, "namespace": null, "name": "platform/plugins/real-estate/src/Supports/CurrencySupport.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Supports\\CurrencySupport.php", "line": 41}, {"index": 18, "namespace": null, "name": "platform/plugins/real-estate/helpers/currencies.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\helpers\\currencies.php", "line": 142}, {"index": 28, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.832751, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 94.444, "width_percent": 5.556}]}, "models": {"data": {"Xmetr\\RealEstate\\Models\\Currency": {"value": 45, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FCurrency.php:1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "Xmetr\\Slug\\Models\\Slug": {"value": 32, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Fslug%2Fsrc%2FModels%2FSlug.php:1", "ajax": false, "filename": "Slug.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\Property": {"value": 20, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FProperty.php:1", "ajax": false, "filename": "Property.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\Category": {"value": 20, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FCategory.php:1", "ajax": false, "filename": "Category.php", "line": "?"}}, "Xmetr\\Location\\Models\\City": {"value": 3, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flocation%2Fsrc%2FModels%2FCity.php:1", "ajax": false, "filename": "City.php", "line": "?"}}, "Xmetr\\Location\\Models\\State": {"value": 2, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flocation%2Fsrc%2FModels%2FState.php:1", "ajax": false, "filename": "State.php", "line": "?"}}}, "count": 122, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://xmetr.gc/en/ajax/properties/map?page=53", "action_name": "public.ajax.properties.map", "controller_action": "Theme\\Xmetr\\Http\\Controllers\\XmetrController@ajaxGetPropertiesForMap", "uri": "GET en/ajax/properties/map", "controller": "Theme\\Xmetr\\Http\\Controllers\\XmetrController@ajaxGetPropertiesForMap<a href=\"vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fsrc%2FHttp%2FControllers%2FXmetrController.php:57\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "en/ajax", "file": "<a href=\"vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fsrc%2FHttp%2FControllers%2FXmetrController.php:57\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/themes/xmetr/src/Http/Controllers/XmetrController.php:57-91</a>", "middleware": "web, core, localeSessionRedirect, localizationRedirect, Xmetr\\Base\\Http\\Middleware\\RequiresJsonRequestMiddleware", "duration": "1.32s", "peak_memory": "62MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1761715600 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>page</span>\" => \"<span class=sf-dump-str title=\"2 characters\">53</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1761715600\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-658897289 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-658897289\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-905955983 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">xmetr.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">https://xmetr.gc/en/rent-properties</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1709 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjE5eVFoNXI3S3dCZTVpWVUvb1ZlTkE9PSIsInZhbHVlIjoiVFFEOHg1MHRNUCtIQndDL1FUdThDSVJxSzFQTTlRRWNjUGJtalZRTmpvRFQrQ1UybGVrWnR6dUN0ZU16VVJXRWg0UVg5cFdaQmlmL212RUdPSjJLcUgray8wa3JyeFU3LzUrRUs3by9jVXM1cCt0MDZCTXZjczI2RTRNR0xqeTBJT1dpcWNNQTdEeW8wYkZ1S3lUc1pZSVFCd2FiN2EvdTRmZGdiU2FOYVREVUtPdGw5SG1xOUZHVU5Db0Zwa1lvVTRXRDFPOXBhNnZOQ3hkWjJjU2JtaDRQRU9WYXRwczhPTmM1Wit2aThBcz0iLCJtYWMiOiI0NThmZWI3NDI2NThmMTIxOWRlN2JiMjljY2Y3MjQzMDUzNmQyYjA5NzllZTg4YmY2ZTc5MGExYjEzODJhMGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.2064844405.1743885089; cookie_for_consent=1; _hjSessionUser_6417422=eyJpZCI6IjhkMzA2ODc1LTcwNjctNTU3Yi1hNDljLTdhNmEyNTdkM2QxOCIsImNyZWF0ZWQiOjE3NDgzODAwMDM5NTcsImV4aXN0aW5nIjp0cnVlfQ==; wishlist=657; project_wishlist=25; _hjSession_6417422=eyJpZCI6IjI0YmIzN2FjLTU3Y2ItNDg2OC1hNWM5LWM5N2QyZTRmMmI5ZCIsImMiOjE3NTM1NTIyNTUyNzUsInMiOjEsInIiOjAsInNiIjowLCJzciI6MCwic2UiOjAsImZzIjowfQ==; _ga_KQ6X76DET4=GS2.1.s1753545705$o107$g1$t1753556681$j47$l0$h0; XSRF-TOKEN=eyJpdiI6IkhtWEtmckRDaUQzeHF2ZGQ1NHpSc3c9PSIsInZhbHVlIjoiYnF2R1h6YUgyL3hFRUNtRnhldHQxd0lld3dzbEt1RXNma0lwUEpNQzJFUXNvWFIxUUs5a1BBdlhYYjZNVkdUZkZhVW1uK0daenlBNFZmRzdnQmIrSFNFTGVud3F5SlcrYnRtTnl3QmF0NkN3ak9sa3R5Tm1YemhWMktSSmI5Z1UiLCJtYWMiOiIyOWVjZWMxMWZkNWIzNTliMTBiN2IzODMxMWZlZjc2ZjI5YTJlYjY0MTQwOTE3ZmI4MGNlZDAyNDQxNGU2YTA3IiwidGFnIjoiIn0%3D; xmetr_session=eyJpdiI6ImdIZGxhWVlPeU1ZSW5Jclp1Z2p4Mnc9PSIsInZhbHVlIjoiZmpPRHA0UHg3MWE5aG1oODJOYzRwb2QvcWN2eThVNjBXN1BZOFJTUmdiNnZzVTlPeTFHOHdlMXU3NCt4MFFOenQ4Q25aNTNBdEEwam85SGZjNDRCZEZYVTFaUU1STGdEcnBiR2ZBMGNYL1Z6RW9jR3FKQUNaM09aVzhZL1I3MC8iLCJtYWMiOiIwOWJiMTUyMTdmMGNmOGZlZmVhZGJhY2NmOGVjNzc2NmY3MDlkMzA1ZWRhYzVkNjEwYTViMDY5NzY0MjE4ZWZkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-905955983\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1013525351 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|1uQwj4bI0uiYsJi0APplQif6Zji5Zq40vBOmafc1oj6NYK3vu89uqM3GLC7O|$2y$04$Astns3SXblBceX03zRicBuVKoyIRRFaOY1GuvREgnRYhHEelfNbdu</span>\"\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>_hjSessionUser_6417422</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>wishlist</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>project_wishlist</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_hjSession_6417422</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_KQ6X76DET4</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6HXj4kO8Ajm1LU4XOruTwqNCwBTw8y5aWqdRmGMM</span>\"\n  \"<span class=sf-dump-key>xmetr_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">J6KlajMTQR9MsRDqe3w8eUJYVHSwE6lYDCR1b5cF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1013525351\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1776870832 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 26 Jul 2025 19:05:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1776870832\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6HXj4kO8Ajm1LU4XOruTwqNCwBTw8y5aWqdRmGMM</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">https://xmetr.gc/en/rent-properties</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>viewed_property</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>1172</span> => <span class=sf-dump-num>**********</span>\n    <span class=sf-dump-key>1171</span> => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>viewed_property_daily</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>1172</span> => <span class=sf-dump-num>**********</span>\n    <span class=sf-dump-key>1171</span> => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_account_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>01K141WAKZRWPF594D6V042QBD</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>01K141ZBJ09KN7HS7QKGWGE4DJ</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://xmetr.gc/en/ajax/properties/map?page=53", "action_name": "public.ajax.properties.map", "controller_action": "Theme\\Xmetr\\Http\\Controllers\\XmetrController@ajaxGetPropertiesForMap"}, "badge": null}}